import { useCallback, useState } from "react";
import type { ConnectionDetails } from "../api/connection-details";

interface UseVoiceAssistantConnectionReturn {
  connect: () => Promise<void>;
  isConnecting: boolean;
  connectionDetails: ConnectionDetails | null;
  error: Error | null;
}

export default function useVoiceAssistantConnection(): UseVoiceAssistantConnectionReturn {
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionDetails, setConnectionDetails] = useState<ConnectionDetails | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const connect = useCallback(async () => {
    console.log("useVoiceAssistantConnection.connect - Starting connection");
    setIsConnecting(true);
    setError(null);

    try {
      // For development, we'll use mock connection details
      // In production, this would call your actual LiveKit server
      const data: ConnectionDetails = {
        serverUrl: process.env.LIVEKIT_URL || "wss://your-livekit-server.com",
        roomName: `voice_assistant_room_${Math.floor(Math.random() * 10_000)}`,
        participantToken: "mock-token", // In production, generate actual token
        participantName: `voice_assistant_user_${Math.floor(Math.random() * 10_000)}`,
      };

      console.log("useVoiceAssistantConnection.connect - Connection details received:", data);
      setConnectionDetails(data);
    } catch (err) {
      console.error("Error connecting to voice assistant:", err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setIsConnecting(false);
    }
  }, []);

  return {
    connect,
    isConnecting,
    connectionDetails,
    error,
  };
}
