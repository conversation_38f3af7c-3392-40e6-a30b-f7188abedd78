export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
};

// For development, we'll use mock connection details
// In production, this would call your actual LiveKit server API
export async function getConnectionDetails(): Promise<ConnectionDetails> {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const roomName = `voice_assistant_room_${Math.floor(Math.random() * 10_000)}`;
  const participantName = `voice_assistant_user_${Math.floor(Math.random() * 10_000)}`;

  // For development, return mock data
  // In production, you would call your server to generate a real token
  const connectionDetails: ConnectionDetails = {
    serverUrl: "wss://derek-8w1qddyd.livekit.cloud",
    roomName,
    participantName,
    participantToken: "mock-token-for-development", // Replace with real token from your server
  };

  return connectionDetails;
}
