import { AccessToken } from "livekit-server-sdk";

export type ConnectionDetails = {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
};

const LIVEKIT_URL = process.env.LIVEKIT_URL;
const API_KEY = process.env.LIVEKIT_API_KEY;
const API_SECRET = process.env.LIVEKIT_API_SECRET;

async function createParticipantToken(
  userInfo: { identity: string },
  roomName: string
): Promise<string> {
  const at = new AccessToken(API_KEY, API_SECRET, userInfo);
  at.addGrant({ roomJoin: true, room: roomName });
  return await at.toJwt();
}

export async function getConnectionDetails(): Promise<ConnectionDetails> {
  if (LIVEKIT_URL === undefined) {
    throw new Error("LIVEKIT_URL is not defined");
  }
  if (API_KEY === undefined) {
    throw new Error("LIVEKIT_API_KEY is not defined");
  }
  if (API_SECRET === undefined) {
    throw new Error("LIVEKIT_API_SECRET is not defined");
  }

  // Generate participant token
  const participantIdentity = `voice_assistant_user_${Math.floor(Math.random() * 10_000)}`;
  const roomName = `voice_assistant_room_${Math.floor(Math.random() * 10_000)}`;
  const participantToken = await createParticipantToken(
    { identity: participantIdentity },
    roomName
  );

  // Return connection details
  const data: ConnectionDetails = {
    serverUrl: LIVEKIT_URL,
    roomName,
    participantToken: participantToken,
    participantName: participantIdentity,
  };

  return data;
}

export async function GET() {
  try {
    const data = await getConnectionDetails();

    return new Response(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  } catch (error) {
    console.error("Error generating connection details:", error);
    return new Response(JSON.stringify({ error: "Failed to generate connection details" }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
