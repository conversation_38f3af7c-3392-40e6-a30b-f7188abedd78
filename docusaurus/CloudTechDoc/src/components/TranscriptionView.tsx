import useCombinedTranscriptions from "../hooks/useCombinedTranscriptions";
import * as React from "react";

export default function TranscriptionView() {
  console.log("TranscriptionView - Component rendering");
  const combinedTranscriptions = useCombinedTranscriptions();
  const containerRef = React.useRef<HTMLDivElement>(null);

  // scroll to bottom when new transcription is added
  React.useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, [combinedTranscriptions]);

  return (
    <div style={{
      position: 'relative',
      height: '200px',
      width: '100%',
      maxWidth: '90vw',
      margin: '0 auto'
    }}>
      {/* Fade-out gradient mask */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '32px',
        background: 'linear-gradient(to bottom, var(--ifm-background-color, #ffffff) 0%, transparent 100%)',
        zIndex: 10,
        pointerEvents: 'none'
      }} />
      <div style={{
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: '32px',
        background: 'linear-gradient(to top, var(--ifm-background-color, #ffffff) 0%, transparent 100%)',
        zIndex: 10,
        pointerEvents: 'none'
      }} />

      {/* Scrollable content */}
      <div 
        ref={containerRef} 
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          gap: '16px',
          overflowY: 'auto',
          padding: '32px 16px',
          backgroundColor: 'var(--ifm-color-emphasis-100, #fafafa)'
        }}
      >
        {combinedTranscriptions.map((segment) => (
          <div
            id={segment.id}
            key={segment.id}
            style={{
              display: 'flex',
              justifyContent: segment.role === "assistant" ? 'flex-start' : 'flex-end'
            }}
          >
            <div
              style={{
                maxWidth: '75%',
                borderRadius: '12px',
                padding: '12px',
                background: segment.role === "assistant"
                  ? 'var(--ifm-color-primary-lightest, #ffffff)'
                  : 'var(--ifm-color-primary, #2563eb)',
                color: segment.role === "assistant" 
                  ? 'var(--ifm-color-content, #374151)' 
                  : 'white',
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                border: segment.role === "assistant" ? '1px solid var(--ifm-color-emphasis-300, #e5e7eb)' : 'none'
              }}
            >
              {segment.text}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
