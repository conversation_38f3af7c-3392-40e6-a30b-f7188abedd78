import React, { useCallback, useState } from 'react';
import { AnimatePresence, motion } from "framer-motion";
import {
  BarVisualizer,
  DisconnectButton,
  RoomAudioRenderer,
  RoomContext,
  VideoTrack,
  VoiceAssistantControlBar,
  useVoiceAssistant,
} from "@livekit/components-react";
import { Room } from "livekit-client";
import TranscriptionView from "./TranscriptionView";
import useVoiceAssistantConnection from "../hooks/useVoiceAssistantConnection";
import { Mic, MicOff, Phone, PhoneOff } from 'lucide-react';

interface VoiceChatProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function VoiceChat({ isOpen, onClose }: VoiceChatProps) {
  console.log("VoiceChat - Component rendering, isOpen:", isOpen);
  const [room] = useState(new Room());
  const { connect, connectionDetails, isConnecting, error } = useVoiceAssistantConnection();

  const onConnectButtonClicked = useCallback(async () => {
    console.log("VoiceChat.onConnectButtonClicked - Starting connection");
    try {
      await connect();

      if (connectionDetails) {
        console.log("VoiceChat.onConnectButtonClicked - Connection details:", connectionDetails);

        await room.connect(
          connectionDetails.serverUrl,
          connectionDetails.participantToken
        );
        console.log("VoiceChat.onConnectButtonClicked - Connected to room");
      }
    } catch (error) {
      console.error("Failed to connect to voice chat:", error);
    }
  }, [room, connect, connectionDetails]);

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 1000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'var(--ifm-background-color, white)',
        borderRadius: '12px',
        padding: '24px',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '80vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 25px 50px rgba(0,0,0,0.25)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          paddingBottom: '16px',
          borderBottom: '1px solid var(--ifm-color-emphasis-300, #e5e7eb)'
        }}>
          <h3 style={{ margin: 0, color: 'var(--ifm-color-content)' }}>Voice Assistant</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: 'var(--ifm-color-content)',
              padding: '4px'
            }}
          >
            ×
          </button>
        </div>

        {/* Voice Chat Content */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <RoomContext.Provider value={room}>
            <SimpleVoiceAssistant onConnectButtonClicked={onConnectButtonClicked} />
          </RoomContext.Provider>
        </div>
      </div>
    </div>
  );
}

function SimpleVoiceAssistant(props: { onConnectButtonClicked: () => void }) {
  console.log("SimpleVoiceAssistant - Component rendering");
  const { state: agentState } = useVoiceAssistant();

  return (
    <>
      <AnimatePresence mode="wait">
        {agentState === "disconnected" ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px'
            }}
          >
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              style={{
                textTransform: 'uppercase',
                padding: '12px 24px',
                backgroundColor: 'var(--ifm-color-primary)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600'
              }}
              onClick={() => props.onConnectButtonClicked()}
            >
              Start a conversation
            </motion.button>
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '16px',
              height: '100%'
            }}
          >
            <AgentVisualizer />
            <div style={{ flex: 1, width: '100%' }}>
              <TranscriptionView />
            </div>
            <div style={{ width: '100%' }}>
              <ControlBar onConnectButtonClicked={props.onConnectButtonClicked} />
            </div>
            <RoomAudioRenderer />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

function AgentVisualizer() {
  console.log("AgentVisualizer - Component rendering");
  const { state: agentState, videoTrack, audioTrack } = useVoiceAssistant();

  if (videoTrack) {
    return (
      <div style={{
        height: '200px',
        width: '200px',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <VideoTrack trackRef={videoTrack} />
      </div>
    );
  }
  return (
    <div style={{ height: '120px', width: '100%' }}>
      <BarVisualizer
        state={agentState}
        barCount={5}
        trackRef={audioTrack}
        options={{ minHeight: 24 }}
      />
    </div>
  );
}

function ControlBar(props: { onConnectButtonClicked: () => void }) {
  console.log("ControlBar - Component rendering");
  const { state: agentState } = useVoiceAssistant();

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      gap: '12px',
      padding: '16px 0'
    }}>
      <VoiceAssistantControlBar controls={{ leave: false }} />
      <DisconnectButton>
        <PhoneOff size={20} />
      </DisconnectButton>
    </div>
  );
}
