import React, { useCallback, useState, useEffect, useRef } from 'react';
import { AnimatePresence, motion } from "framer-motion";
import { Mic, MicOff, PhoneOff } from 'lucide-react';

interface VoiceChatProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Message {
  id: string;
  text: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export default function VoiceChat({ isOpen, onClose }: VoiceChatProps) {
  console.log("VoiceChat - Component rendering, isOpen:", isOpen);
  const [isConnected, setIsConnected] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! I\'m your voice assistant. Click "Start a conversation" to begin speaking with me.',
      role: 'assistant',
      timestamp: new Date()
    }
  ]);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);

  const onConnectButtonClicked = useCallback(async () => {
    console.log("VoiceChat.onConnectButtonClicked - Starting connection");
    try {
      // Check if browser supports speech recognition
      if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
        alert('Your browser does not support speech recognition. Please use Chrome, Edge, or Safari.');
        return;
      }

      setIsConnected(true);

      // Add a welcome message
      const welcomeMessage: Message = {
        id: Date.now().toString(),
        text: 'Great! I\'m now listening. You can start speaking, and I\'ll respond to you.',
        role: 'assistant',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, welcomeMessage]);

    } catch (error) {
      console.error("Failed to connect to voice chat:", error);
    }
  }, []);

  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 1000,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'var(--ifm-background-color, white)',
        borderRadius: '12px',
        padding: '24px',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '80vh',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 25px 50px rgba(0,0,0,0.25)'
      }}>
        {/* Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          paddingBottom: '16px',
          borderBottom: '1px solid var(--ifm-color-emphasis-300, #e5e7eb)'
        }}>
          <h3 style={{ margin: 0, color: 'var(--ifm-color-content)' }}>Voice Assistant</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: 'var(--ifm-color-content)',
              padding: '4px'
            }}
          >
            ×
          </button>
        </div>

        {/* Voice Chat Content */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          <SimpleVoiceAssistant onConnectButtonClicked={onConnectButtonClicked} />
        </div>
      </div>
    </div>
  );
}

function SimpleVoiceAssistant(props: { onConnectButtonClicked: () => void }) {
  console.log("SimpleVoiceAssistant - Component rendering");
  const [isConnected, setIsConnected] = useState(false);

  const handleConnect = useCallback(async () => {
    console.log("SimpleVoiceAssistant.handleConnect - Starting connection");
    await props.onConnectButtonClicked();
    setIsConnected(true);
  }, [props]);

  const handleDisconnect = useCallback(() => {
    console.log("SimpleVoiceAssistant.handleDisconnect - Disconnecting");
    setIsConnected(false);
  }, []);

  return (
    <>
      <AnimatePresence mode="wait">
        {!isConnected ? (
          <motion.div
            key="disconnected"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '200px'
            }}
          >
            <motion.button
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              style={{
                textTransform: 'uppercase',
                padding: '12px 24px',
                backgroundColor: 'var(--ifm-color-primary)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600'
              }}
              onClick={handleConnect}
            >
              Start a conversation
            </motion.button>
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '16px',
              height: '100%'
            }}
          >
            <SimpleAgentVisualizer />
            <div style={{ flex: 1, width: '100%' }}>
              <SimpleTranscriptionView />
            </div>
            <div style={{ width: '100%' }}>
              <SimpleControlBar onDisconnect={handleDisconnect} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

function SimpleAgentVisualizer() {
  console.log("SimpleAgentVisualizer - Component rendering");
  const [isListening, setIsListening] = useState(false);

  useEffect(() => {
    // Simulate listening animation
    const interval = setInterval(() => {
      setIsListening(prev => !prev);
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div style={{
      height: '120px',
      width: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'var(--ifm-color-emphasis-100)',
      borderRadius: '8px',
      border: '2px solid var(--ifm-color-primary)',
      position: 'relative'
    }}>
      <div style={{
        width: '60px',
        height: '60px',
        borderRadius: '50%',
        backgroundColor: 'var(--ifm-color-primary)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transform: isListening ? 'scale(1.1)' : 'scale(1)',
        transition: 'transform 0.3s ease'
      }}>
        <Mic size={24} color="white" />
      </div>
      <div style={{
        position: 'absolute',
        bottom: '8px',
        fontSize: '12px',
        color: 'var(--ifm-color-content-secondary)'
      }}>
        {isListening ? 'Listening...' : 'Ready to listen'}
      </div>
    </div>
  );
}

function SimpleTranscriptionView() {
  console.log("SimpleTranscriptionView - Component rendering");
  const [messages, setMessages] = useState([
    { id: '1', text: 'Voice chat is ready! Start speaking to begin the conversation.', role: 'assistant' }
  ]);

  return (
    <div style={{
      height: '200px',
      width: '100%',
      backgroundColor: 'var(--ifm-color-emphasis-100)',
      borderRadius: '8px',
      padding: '16px',
      overflowY: 'auto',
      border: '1px solid var(--ifm-color-emphasis-300)'
    }}>
      {messages.map((message) => (
        <div
          key={message.id}
          style={{
            display: 'flex',
            justifyContent: message.role === 'assistant' ? 'flex-start' : 'flex-end',
            marginBottom: '12px'
          }}
        >
          <div
            style={{
              maxWidth: '75%',
              padding: '8px 12px',
              borderRadius: '12px',
              backgroundColor: message.role === 'assistant'
                ? 'var(--ifm-color-primary-lightest)'
                : 'var(--ifm-color-primary)',
              color: message.role === 'assistant'
                ? 'var(--ifm-color-content)'
                : 'white',
              fontSize: '14px'
            }}
          >
            {message.text}
          </div>
        </div>
      ))}
    </div>
  );
}

function SimpleControlBar(props: { onDisconnect: () => void }) {
  console.log("SimpleControlBar - Component rendering");
  const [isMuted, setIsMuted] = useState(false);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      gap: '12px',
      padding: '16px 0'
    }}>
      <button
        onClick={() => setIsMuted(!isMuted)}
        style={{
          padding: '12px',
          borderRadius: '50%',
          border: 'none',
          backgroundColor: isMuted ? 'var(--ifm-color-danger)' : 'var(--ifm-color-emphasis-300)',
          color: isMuted ? 'white' : 'var(--ifm-color-content)',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.2s ease'
        }}
        title={isMuted ? 'Unmute' : 'Mute'}
      >
        {isMuted ? <MicOff size={20} /> : <Mic size={20} />}
      </button>
      <button
        onClick={props.onDisconnect}
        style={{
          padding: '12px',
          borderRadius: '50%',
          border: 'none',
          backgroundColor: 'var(--ifm-color-danger)',
          color: 'white',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          transition: 'all 0.2s ease'
        }}
        title="End conversation"
      >
        <PhoneOff size={20} />
      </button>
    </div>
  );
}
