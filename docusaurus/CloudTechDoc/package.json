{"name": "cloud-tech-doc", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc"}, "dependencies": {"@docusaurus/core": "3.8.0", "@docusaurus/preset-classic": "3.8.0", "@livekit/components-react": "^2.9.3", "@livekit/components-styles": "^1.1.4", "@mdx-js/react": "^3.0.0", "clsx": "^2.0.0", "framer-motion": "^11.18.0", "livekit-client": "^2.8.0", "livekit-server-sdk": "^2.9.7", "lucide-react": "^0.511.0", "prism-react-renderer": "^2.3.0", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "3.8.0", "@docusaurus/tsconfig": "3.8.0", "@docusaurus/types": "3.8.0", "typescript": "~5.6.2"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}